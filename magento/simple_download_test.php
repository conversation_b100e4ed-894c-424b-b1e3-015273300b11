<?php
/**
 * Simple Download Test (without <PERSON><PERSON>o)
 * Test Bitbucket download directly
 */

echo "=== Simple Download Test ===\n\n";

// Configuration - UPDATE THESE VALUES
$workspace = 'pfg';  // Your Bitbucket workspace
$username = 'martinpfg';  // Your Bitbucket username
$password = '';  // Your Bitbucket app password - SET THIS!
$repositoryName = 'pfg-cloudflare-integration';  // Repository to test
$ref = 'master';

echo "Testing download of: $repositoryName ($ref)\n";
echo "Workspace: $workspace\n";
echo "Username: $username\n";
echo "Password: " . (empty($password) ? 'NOT SET - PLEASE UPDATE SCRIPT' : 'SET') . "\n\n";

if (empty($password)) {
    echo "❌ Please edit this script and set your Bitbucket app password\n";
    echo "You can find it in System > Configuration > PFG > Core Module Management\n";
    exit(1);
}

// Build download URL
$downloadUrl = sprintf('https://bitbucket.org/%s/%s/get/%s.zip', $workspace, $repositoryName, $ref);
echo "Download URL: $downloadUrl\n\n";

// Test download
echo "Testing cURL download...\n";

$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => $downloadUrl,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
    CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
    CURLOPT_USERPWD => $username . ':' . $password,
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2
));

$zipData = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
$downloadSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Content Type: $contentType\n";
echo "Download Size: " . number_format($downloadSize) . " bytes\n";
echo "cURL Error: " . ($error ?: 'None') . "\n\n";

if ($httpCode !== 200) {
    echo "❌ HTTP Error: $httpCode\n";
    if ($httpCode === 401) {
        echo "This is an authentication error. Check your credentials.\n";
    } elseif ($httpCode === 404) {
        echo "Repository not found. Check repository name and workspace.\n";
    }
    
    // Show response content for debugging
    if (!empty($zipData) && strlen($zipData) < 1000) {
        echo "Response content:\n";
        echo $zipData . "\n";
    }
    exit(1);
}

if (empty($zipData)) {
    echo "❌ No data received\n";
    exit(1);
}

echo "✅ Download successful!\n\n";

// Analyze the downloaded data
echo "Analyzing downloaded data...\n";

// Check if it starts with ZIP signature
$zipSignature = substr($zipData, 0, 4);
$isZip = ($zipSignature === "PK\x03\x04" || $zipSignature === "PK\x05\x06" || $zipSignature === "PK\x07\x08");
echo "ZIP signature check: " . ($isZip ? '✅ Valid' : '❌ Invalid') . "\n";

if (!$isZip) {
    echo "First 200 chars of response:\n";
    echo substr($zipData, 0, 200) . "\n\n";
    
    // Check if it's HTML (error page)
    if (strpos($zipData, '<html') !== false || strpos($zipData, '<!DOCTYPE') !== false) {
        echo "❌ Response appears to be HTML (error page)\n";
    }
    exit(1);
}

// Save and test ZIP file
$testFile = '/tmp/test-download.zip';
file_put_contents($testFile, $zipData);

echo "Testing ZIP file: $testFile\n";

$zip = new ZipArchive();
$result = $zip->open($testFile);

if ($result === TRUE) {
    echo "✅ ZIP file is valid\n";
    echo "Files in archive: " . $zip->numFiles . "\n";
    
    // List first few files
    echo "Sample files:\n";
    for ($i = 0; $i < min(10, $zip->numFiles); $i++) {
        $stat = $zip->statIndex($i);
        echo "  - " . $stat['name'] . "\n";
    }
    $zip->close();
    
    echo "\n✅ Download test successful!\n";
    echo "The ZIP file is valid and can be extracted.\n";
    
} else {
    echo "❌ ZIP file is invalid (error code: $result)\n";
    
    $zipErrors = array(
        ZipArchive::ER_NOZIP => 'Not a zip archive',
        ZipArchive::ER_INCONS => 'Zip archive inconsistent', 
        ZipArchive::ER_CRC => 'CRC error',
        ZipArchive::ER_OPEN => 'Can\'t open file',
        ZipArchive::ER_READ => 'Read error'
    );
    
    $errorMsg = isset($zipErrors[$result]) ? $zipErrors[$result] : 'Unknown error';
    echo "Error: $errorMsg\n";
}

// Clean up
if (file_exists($testFile)) {
    unlink($testFile);
    echo "Test file cleaned up\n";
}

echo "\n=== Test Complete ===\n";
?>
