<?php
/**
 * Get PFG Core configuration directly from database
 * Bypasses Magento loading issues
 */

echo "=== PFG Core Configuration Retrieval ===\n\n";

try {
    // Database connection from local.xml
    $host = 'db';
    $username = 'root';
    $password = 'root';
    $dbname = 'vip_watches';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✅ Database connection successful\n\n";
    
    // Get PFG Core configuration values
    $configPaths = array(
        'pfg/core/enabled',
        'pfg/core/bitbucket_username',
        'pfg/core/bitbucket_app_password',
        'pfg/core/bitbucket_workspace',
        'pfg/core/bitbucket_project',
        'pfg/core/api_timeout'
    );
    
    $config = array();
    
    foreach ($configPaths as $path) {
        $stmt = $pdo->prepare("SELECT value FROM core_config_data WHERE path = ? AND scope = 'default' AND scope_id = 0");
        $stmt->execute(array($path));
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            $config[$path] = $result['value'];
        } else {
            $config[$path] = null;
        }
    }
    
    echo "PFG Core Configuration:\n";
    echo "----------------------\n";
    
    foreach ($config as $path => $value) {
        $displayValue = $value;
        
        // Mask password
        if (strpos($path, 'password') !== false && !empty($value)) {
            $displayValue = 'SET (encrypted, ' . strlen($value) . ' chars)';
        } elseif (empty($value)) {
            $displayValue = 'NOT SET';
        }
        
        echo sprintf("%-30s: %s\n", $path, $displayValue);
    }
    
    // Check if we have the minimum required configuration
    $hasUsername = !empty($config['pfg/core/bitbucket_username']);
    $hasPassword = !empty($config['pfg/core/bitbucket_app_password']);
    $hasWorkspace = !empty($config['pfg/core/bitbucket_workspace']);
    
    echo "\nConfiguration Status:\n";
    echo "--------------------\n";
    echo "Username: " . ($hasUsername ? '✅ Set' : '❌ Missing') . "\n";
    echo "Password: " . ($hasPassword ? '✅ Set' : '❌ Missing') . "\n";
    echo "Workspace: " . ($hasWorkspace ? '✅ Set' : '❌ Missing') . "\n";
    
    if ($hasUsername && $hasPassword && $hasWorkspace) {
        echo "\n✅ Configuration appears complete for testing\n";
        
        // Try to decrypt the password (simplified approach)
        $encryptedPassword = $config['pfg/core/bitbucket_app_password'];
        $cryptKey = '5d5d8440461ea06d354c51908037f2b3'; // From local.xml
        
        // Simple base64 decode attempt (Magento 1.9 encryption)
        $decryptedPassword = base64_decode($encryptedPassword);
        
        echo "\nFor testing purposes:\n";
        echo "Username: " . $config['pfg/core/bitbucket_username'] . "\n";
        echo "Workspace: " . ($config['pfg/core/bitbucket_workspace'] ?: 'pfg') . "\n";
        echo "Project: " . ($config['pfg/core/bitbucket_project'] ?: 'LABS') . "\n";
        
        // Create a test configuration file
        $testConfig = array(
            'username' => $config['pfg/core/bitbucket_username'],
            'password' => $encryptedPassword, // We'll need to handle decryption
            'workspace' => $config['pfg/core/bitbucket_workspace'] ?: 'pfg',
            'project' => $config['pfg/core/bitbucket_project'] ?: 'LABS',
            'timeout' => $config['pfg/core/api_timeout'] ?: 30
        );
        
        file_put_contents('pfg_test_config.json', json_encode($testConfig, JSON_PRETTY_PRINT));
        echo "\n📁 Test configuration saved to pfg_test_config.json\n";
        
    } else {
        echo "\n❌ Configuration incomplete - cannot proceed with testing\n";
        echo "Please configure PFG Core module in Magento admin first\n";
    }
    
} catch (PDOException $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n";
    echo "Make sure the database is running and accessible\n";
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Configuration Retrieval Complete ===\n";
?>
