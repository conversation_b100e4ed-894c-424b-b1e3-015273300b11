#!/bin/bash

# Enhanced Downloader Test Runner
# Convenient script to run different test scenarios

echo "=== PFG Downloader Test Runner ==="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to run test and show result
run_test() {
    local test_name="$1"
    local command="$2"
    
    echo -e "${BLUE}Running: $test_name${NC}"
    echo "Command: $command"
    echo "----------------------------------------"
    
    if eval "$command"; then
        echo -e "${GREEN}✅ $test_name: PASSED${NC}"
    else
        local exit_code=$?
        if [ $exit_code -eq 2 ]; then
            echo -e "${YELLOW}⚠️  $test_name: PASSED WITH WARNINGS${NC}"
        else
            echo -e "${RED}❌ $test_name: FAILED${NC}"
        fi
    fi
    echo
}

# Check if enhanced test exists
if [ ! -f "enhanced_downloader_test.php" ]; then
    echo -e "${RED}Error: enhanced_downloader_test.php not found${NC}"
    echo "Please make sure you're running this from the Magento root directory"
    exit 1
fi

# Menu selection
echo "Select test to run:"
echo "1. Quick Test (auto-detect mode)"
echo "2. Standalone Test (no Magento)"
echo "3. Magento Integration Test"
echo "4. Full Test Suite (with performance)"
echo "5. Test Specific Repository"
echo "6. Run All Tests"
echo "7. Legacy Tests (original scripts)"
echo "8. Help"
echo

read -p "Enter your choice (1-8): " choice

case $choice in
    1)
        run_test "Quick Test" "php enhanced_downloader_test.php auto"
        ;;
    2)
        echo -e "${YELLOW}Note: You may need to edit the script to set credentials for standalone mode${NC}"
        run_test "Standalone Test" "php enhanced_downloader_test.php standalone"
        ;;
    3)
        run_test "Magento Integration Test" "php enhanced_downloader_test.php magento --verbose"
        ;;
    4)
        run_test "Full Test Suite" "php enhanced_downloader_test.php full --verbose"
        ;;
    5)
        read -p "Enter repository name: " repo_name
        if [ -n "$repo_name" ]; then
            run_test "Repository Test ($repo_name)" "php enhanced_downloader_test.php auto $repo_name --verbose"
        else
            echo -e "${RED}Repository name cannot be empty${NC}"
        fi
        ;;
    6)
        echo -e "${BLUE}Running all test modes...${NC}"
        echo
        run_test "Quick Test" "php enhanced_downloader_test.php auto"
        run_test "Standalone Test" "php enhanced_downloader_test.php standalone"
        run_test "Magento Integration Test" "php enhanced_downloader_test.php magento"
        run_test "Full Test Suite" "php enhanced_downloader_test.php full"
        ;;
    7)
        echo -e "${BLUE}Running legacy tests...${NC}"
        echo
        
        if [ -f "test_bitbucket_download.php" ]; then
            run_test "Legacy: Bitbucket Download Test" "php test_bitbucket_download.php"
        fi
        
        if [ -f "debug_download.php" ]; then
            run_test "Legacy: Debug Download Test" "php debug_download.php"
        fi
        
        if [ -f "simple_download_test.php" ]; then
            echo -e "${YELLOW}Note: simple_download_test.php requires manual credential configuration${NC}"
            run_test "Legacy: Simple Download Test" "php simple_download_test.php"
        fi
        ;;
    8)
        php enhanced_downloader_test.php --help
        ;;
    *)
        echo -e "${RED}Invalid choice. Please select 1-8.${NC}"
        exit 1
        ;;
esac

echo -e "${BLUE}Test runner completed.${NC}"

# Show additional information
echo
echo "Additional Information:"
echo "- Enhanced test logs are more detailed with --verbose flag"
echo "- Use --no-cleanup to keep downloaded files for inspection"
echo "- Check var/log/pfg_core.log for detailed Magento logs"
echo "- For standalone mode, edit enhanced_downloader_test.php to set credentials"
echo
echo "Available test files:"
ls -la *test*.php *debug*.php 2>/dev/null | grep -E '\.(php)$' || echo "No additional test files found"
