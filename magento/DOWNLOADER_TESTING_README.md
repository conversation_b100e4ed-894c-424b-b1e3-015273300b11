# PFG Downloader Testing Suite

This comprehensive testing suite validates the Bitbucket repository download functionality for the PFG Core module system.

## Overview

The testing suite consists of multiple test scripts designed to validate different aspects of the download system:

### Test Scripts

1. **enhanced_downloader_test.php** - Main comprehensive testing script
2. **test_bitbucket_download.php** - Original Magento-integrated test
3. **debug_download.php** - Detailed step-by-step debugging
4. **simple_download_test.php** - Standalone test without Magento
5. **run_downloader_tests.sh** - Interactive test runner

## Quick Start

### Using the Test Runner (Recommended)
```bash
./run_downloader_tests.sh
```

### Direct Script Usage
```bash
# Auto-detect mode (recommended)
php enhanced_downloader_test.php

# Specific modes
php enhanced_downloader_test.php standalone
php enhanced_downloader_test.php magento --verbose
php enhanced_downloader_test.php full

# Test specific repository
php enhanced_downloader_test.php auto pfg-cloudflare-integration
```

## Test Modes

### Auto Mode (Default)
- Automatically detects if Magento is available
- Falls back to standalone if Magento fails to load
- Best for general testing

### Standalone Mode
- Runs without Magento dependencies
- Requires manual credential configuration in script
- Good for testing basic download functionality

### Magento Mode
- Requires Magento environment
- Uses PFG Core helper classes
- Tests full integration

### Full Mode
- Complete test suite including performance tests
- API connection testing
- Repository listing
- Multiple download iterations for performance metrics

## Configuration

### Magento Mode
Configure credentials in Magento admin:
- Go to System > Configuration > PFG > Core Module Management
- Set Bitbucket Username and App Password
- Configure Workspace and Project settings

### Standalone Mode
Edit `enhanced_downloader_test.php` and update the `getConfiguration()` function:
```php
return array(
    'username' => 'your-username',
    'password' => 'your-app-password',
    'workspace' => 'pfg',
    'project' => 'LABS',
    // ...
);
```

## Test Features

### Comprehensive Validation
- ✅ Configuration validation
- ✅ API connection testing
- ✅ Repository listing
- ✅ Download functionality
- ✅ ZIP file validation
- ✅ Performance metrics
- ✅ Error analysis

### Output Features
- Color-coded results
- Detailed timing information
- Verbose mode for debugging
- Performance statistics
- Error categorization

### Options
- `--verbose, -v` - Detailed output
- `--no-cleanup` - Keep downloaded files
- `--help, -h` - Show usage information

## Troubleshooting

### Common Issues

#### Authentication Errors (HTTP 401)
- Check Bitbucket username and app password
- Verify app password has repository read permissions
- Ensure credentials are properly configured

#### Repository Not Found (HTTP 404)
- Verify repository name spelling
- Check workspace and project settings
- Ensure repository exists and is accessible

#### ZIP File Invalid
- Network connectivity issues
- Incomplete downloads
- Server-side errors

#### Magento Environment Issues
- Check if Magento is properly installed
- Verify PFG Core module is enabled
- Check file permissions

### Debug Steps

1. **Run with verbose output:**
   ```bash
   php enhanced_downloader_test.php auto --verbose
   ```

2. **Test specific repository:**
   ```bash
   php enhanced_downloader_test.php auto repository-name --verbose
   ```

3. **Use debug script for detailed analysis:**
   ```bash
   php debug_download.php
   ```

4. **Check Magento logs:**
   ```bash
   tail -f var/log/pfg_core.log
   ```

## Legacy Scripts

### test_bitbucket_download.php
Original comprehensive test using Magento helpers:
- Tests credentials, connection, repositories, and downloads
- Validates ZIP files
- Includes cleanup

### debug_download.php
Step-by-step debugging script:
- Detailed cURL analysis
- HTTP response inspection
- ZIP signature validation
- Error diagnosis

### simple_download_test.php
Minimal standalone test:
- No Magento dependencies
- Direct cURL implementation
- Basic validation

## Performance Testing

The full test mode includes performance metrics:
- Multiple download iterations
- Average, minimum, and maximum times
- Comparison across repositories
- Network performance analysis

## Integration with PFG Core

The testing suite integrates with PFG Core module features:
- Encrypted credential storage
- Centralized configuration
- Enhanced logging
- Error reporting
- Backup and cleanup functionality

## Development and Maintenance

### Adding New Tests
1. Add test function to `enhanced_downloader_test.php`
2. Call `addTestResult()` with appropriate status
3. Include timing and detail information
4. Update documentation

### Extending Functionality
- Add new test modes in main script
- Update test runner menu
- Include new options in help text
- Test thoroughly across all modes

## Security Considerations

- Credentials are encrypted in Magento mode
- Temporary files are cleaned up automatically
- SSL verification is enforced
- App passwords are used instead of main passwords
- Sensitive information is masked in output

## Support

For issues or questions:
1. Check this documentation
2. Run tests in verbose mode
3. Review Magento logs
4. Check PFG Core module configuration
5. Verify Bitbucket repository access
