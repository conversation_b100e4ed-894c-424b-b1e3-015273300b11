<?php
/**
 * Final Comprehensive Downloader Test
 * Tests all available repositories and provides detailed analysis
 */

echo "=== Final Downloader Test Results ===\n\n";

// Configuration with proper credentials
$username = 'mkonstantinov';
$password = 'ATBB8apM9szgWyfhk2FCfLSXt2Dp8A61F116';
$workspace = 'pfg';
$project = 'LABS';

echo "Testing Bitbucket repository downloads for PFG modules\n";
echo "Workspace: $workspace\n";
echo "Project: $project\n";
echo "Authentication: ✅ Working\n\n";

// Get all repositories
echo "1. Fetching repository list...\n";
$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => "https://api.bitbucket.org/2.0/repositories/$workspace?q=" . urlencode('project.key="' . $project . '"') . "&sort=name&pagelen=50",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
    CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
    CURLOPT_USERPWD => $username . ':' . $password,
    CURLOPT_HTTPHEADER => array('Accept: application/json'),
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2
));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode !== 200) {
    echo "❌ Failed to fetch repositories (HTTP $httpCode)\n";
    exit(1);
}

$repoData = json_decode($response, true);
$repositories = array();

foreach ($repoData['values'] as $repo) {
    $slug = explode('/', $repo['full_name'])[1];
    $repositories[] = array(
        'name' => $repo['name'],
        'slug' => $slug,
        'description' => $repo['description'] ?: 'No description',
        'updated' => $repo['updated_on']
    );
}

echo "✅ Found " . count($repositories) . " repositories\n\n";

// Test each repository
echo "2. Testing downloads for each repository:\n";
echo str_repeat("-", 80) . "\n";

$results = array();
$totalSize = 0;
$successCount = 0;

foreach ($repositories as $repo) {
    echo sprintf("Testing: %-25s (%s)\n", $repo['name'], $repo['slug']);
    
    $downloadUrl = "https://bitbucket.org/$workspace/{$repo['slug']}/get/master.zip";
    
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => $downloadUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
        CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
        CURLOPT_USERPWD => $username . ':' . $password,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2
    ));
    
    $startTime = microtime(true);
    $data = curl_exec($ch);
    $downloadTime = microtime(true) - $startTime;
    
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $downloadSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
    $error = curl_error($ch);
    curl_close($ch);
    
    $result = array(
        'repo' => $repo,
        'success' => false,
        'http_code' => $httpCode,
        'size' => $downloadSize,
        'time' => $downloadTime,
        'error' => $error,
        'zip_valid' => false,
        'file_count' => 0
    );
    
    if ($httpCode === 200 && $downloadSize > 0 && empty($error)) {
        // Check if it's a valid ZIP
        $zipSignature = substr($data, 0, 4);
        $isZip = ($zipSignature === "PK\x03\x04" || $zipSignature === "PK\x05\x06" || $zipSignature === "PK\x07\x08");
        
        if ($isZip) {
            $tempFile = "/tmp/test-{$repo['slug']}-" . time() . ".zip";
            file_put_contents($tempFile, $data);
            
            $zip = new ZipArchive();
            if ($zip->open($tempFile) === TRUE) {
                $result['success'] = true;
                $result['zip_valid'] = true;
                $result['file_count'] = $zip->numFiles;
                $zip->close();
                $successCount++;
                $totalSize += $downloadSize;
                
                echo "  ✅ SUCCESS - " . number_format($downloadSize) . " bytes, {$result['file_count']} files (" . sprintf("%.2fs", $downloadTime) . ")\n";
            } else {
                echo "  ❌ FAILED - Invalid ZIP file\n";
            }
            
            unlink($tempFile);
        } else {
            echo "  ❌ FAILED - Not a ZIP file\n";
        }
    } else {
        $errorMsg = $error ?: "HTTP $httpCode";
        echo "  ❌ FAILED - $errorMsg\n";
    }
    
    $results[] = $result;
}

// Summary
echo "\n" . str_repeat("=", 80) . "\n";
echo "DOWNLOAD TEST SUMMARY\n";
echo str_repeat("=", 80) . "\n";

echo sprintf("Total Repositories: %d\n", count($repositories));
echo sprintf("Successful Downloads: %d (%.1f%%)\n", $successCount, ($successCount / count($repositories)) * 100);
echo sprintf("Failed Downloads: %d\n", count($repositories) - $successCount);
echo sprintf("Total Downloaded Size: %s\n", formatBytes($totalSize));

$avgTime = array_sum(array_column($results, 'time')) / count($results);
echo sprintf("Average Download Time: %.2f seconds\n", $avgTime);

echo "\nSUCCESSFUL DOWNLOADS:\n";
foreach ($results as $result) {
    if ($result['success']) {
        echo sprintf("✅ %-25s - %s (%d files)\n", 
            $result['repo']['name'], 
            formatBytes($result['size']), 
            $result['file_count']
        );
    }
}

if ($successCount < count($repositories)) {
    echo "\nFAILED DOWNLOADS:\n";
    foreach ($results as $result) {
        if (!$result['success']) {
            $errorMsg = $result['error'] ?: "HTTP {$result['http_code']}";
            echo sprintf("❌ %-25s - %s\n", $result['repo']['name'], $errorMsg);
        }
    }
}

echo "\n" . str_repeat("=", 80) . "\n";

if ($successCount === count($repositories)) {
    echo "🎉 ALL REPOSITORIES DOWNLOADED SUCCESSFULLY!\n";
    echo "The downloader is working perfectly for all PFG modules.\n";
} else {
    echo "⚠️  SOME DOWNLOADS FAILED\n";
    echo "Most repositories are working. Failed ones may have different branch names or access issues.\n";
}

echo "\nRECOMMENDATIONS:\n";
echo "- Use repository slugs (not display names) for downloads\n";
echo "- Use 'master' branch for most repositories\n";
echo "- For failed repos, try 'HEAD' or 'main' branch instead\n";
echo "- Download URL format: https://bitbucket.org/pfg/{slug}/get/master.zip\n";

function formatBytes($bytes) {
    $units = array('B', 'KB', 'MB', 'GB');
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    return round($bytes, 2) . ' ' . $units[$i];
}

echo "\n=== Test Complete ===\n";
?>
