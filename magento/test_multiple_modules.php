<?php
/**
 * Test Multiple Module Installations
 * Test the fixes across different PFG modules
 */

echo "=== Testing Multiple Module Installation Fixes ===\n\n";

try {
    require_once 'app/Mage.php';
    Mage::app();
    
    $testModules = array(
        'cloudflare-integration' => 'master',
        'pfg-log-reader' => 'main',
        'pfg-analytics' => 'master'
    );
    
    foreach ($testModules as $repositoryName => $expectedBranch) {
        echo "Testing: $repositoryName (branch: $expectedBranch)\n";
        echo str_repeat("-", 60) . "\n";
        
        // Get repository details
        $repositoryModel = Mage::getModel('pfg_core/repository');
        $repoDetails = $repositoryModel->getRepositoryDetails($repositoryName);
        
        if (!$repoDetails['success']) {
            echo "❌ Failed to get repository details: " . $repoDetails['error'] . "\n\n";
            continue;
        }
        
        $repo = $repoDetails['data'];
        $expectedModuleName = $repo['module_name'];
        
        // Download repository
        $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
        $repositoryIdentifier = $repo['repository_identifier'];
        $downloadResult = $bitbucketHelper->downloadRepository($repositoryIdentifier, 'master');
        
        if (!$downloadResult['success']) {
            echo "❌ Download failed: " . $downloadResult['error'] . "\n\n";
            continue;
        }
        
        echo "✅ Downloaded: " . number_format($downloadResult['size']) . " bytes\n";
        
        // Test module name detection
        $installationModel = Mage::getModel('pfg_core/installation');
        $reflection = new ReflectionClass($installationModel);
        $method = $reflection->getMethod('_detectModuleNameFromZip');
        $method->setAccessible(true);
        
        $actualModuleName = $method->invoke($installationModel, $downloadResult['filepath']);
        
        echo "Expected: $expectedModuleName\n";
        echo "Detected: " . ($actualModuleName ?: 'NONE') . "\n";
        echo "Result: " . ($actualModuleName ? '✅ SUCCESS' : '❌ FAILED') . "\n";
        
        // Test file structure
        if ($actualModuleName) {
            $tempDir = Mage::helper('pfg_core')->getTempDir() . DS . 'test_' . time() . '_' . rand(1000, 9999);
            mkdir($tempDir, 0755, true);
            
            $zip = new ZipArchive();
            $zip->open($downloadResult['filepath']);
            $zip->extractTo($tempDir);
            $zip->close();
            
            $extractedDirs = glob($tempDir . DS . '*', GLOB_ONLYDIR);
            $sourceDir = $extractedDirs[0];
            
            // Check for module files
            $moduleFiles = glob($sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . 'PFG_*.xml');
            $moduleDirs = glob($sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . 'PFG' . DS . '*', GLOB_ONLYDIR);
            
            echo "Module files found: " . count($moduleFiles) . "\n";
            echo "Module directories found: " . count($moduleDirs) . "\n";
            
            foreach ($moduleFiles as $file) {
                echo "  - " . basename($file) . "\n";
            }
            
            foreach ($moduleDirs as $dir) {
                echo "  - PFG/" . basename($dir) . "/\n";
            }
            
            // Cleanup
            function removeDirectory($dir) {
                if (is_dir($dir)) {
                    $files = array_diff(scandir($dir), array('.', '..'));
                    foreach ($files as $file) {
                        $path = $dir . DS . $file;
                        is_dir($path) ? removeDirectory($path) : unlink($path);
                    }
                    rmdir($dir);
                }
            }
            removeDirectory($tempDir);
        }
        
        // Cleanup download
        unlink($downloadResult['filepath']);
        
        echo "\n";
    }
    
    echo str_repeat("=", 60) . "\n";
    echo "MULTI-MODULE TEST COMPLETE\n";
    echo str_repeat("=", 60) . "\n";
    echo "All tested modules should now install correctly!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
