<?php
/**
 * Test Bitbucket API Access
 * Check authentication and repository access
 */

echo "=== Bitbucket API Test ===\n\n";

// Configuration from database
$username = 'mkonstantinov';
$encryptedPassword = 'nwJvdt7AHNVn0pO2W84ScSwxwCdmWn9cvsh2uEhaYCSwpdkkBlSJCw==';
$workspace = 'pfg';
$project = 'LABS';

// Try to decrypt password
$password = base64_decode($encryptedPassword);

echo "Username: $username\n";
echo "Workspace: $workspace\n";
echo "Project: $project\n";
echo "Password: " . (empty($password) ? 'NOT SET' : 'SET (' . strlen($password) . ' chars)') . "\n\n";

// Test 1: Basic authentication test
echo "1. Testing basic authentication...\n";
$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => 'https://api.bitbucket.org/2.0/user',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
    CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
    CURLOPT_USERPWD => $username . ':' . $password,
    CURLOPT_HTTPHEADER => array(
        'Accept: application/json',
        'Content-Type: application/json'
    ),
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2
));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "   HTTP Code: $httpCode\n";
echo "   cURL Error: " . ($error ?: 'None') . "\n";

if ($httpCode === 200) {
    $userData = json_decode($response, true);
    echo "   ✅ Authentication successful!\n";
    echo "   User: " . $userData['display_name'] . " (" . $userData['username'] . ")\n";
} else {
    echo "   ❌ Authentication failed\n";
    if ($httpCode === 401) {
        echo "   This indicates invalid credentials\n";
    }
    echo "   Response: " . substr($response, 0, 200) . "\n";
}

echo "\n";

// Test 2: List repositories in workspace
if ($httpCode === 200) {
    echo "2. Testing repository listing...\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => "https://api.bitbucket.org/2.0/repositories/$workspace?q=" . urlencode('project.key="' . $project . '"'),
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
        CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
        CURLOPT_USERPWD => $username . ':' . $password,
        CURLOPT_HTTPHEADER => array(
            'Accept: application/json',
            'Content-Type: application/json'
        ),
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2
    ));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "   HTTP Code: $httpCode\n";
    echo "   cURL Error: " . ($error ?: 'None') . "\n";
    
    if ($httpCode === 200) {
        $repoData = json_decode($response, true);
        echo "   ✅ Repository listing successful!\n";
        echo "   Found " . count($repoData['values']) . " repositories:\n";
        
        foreach ($repoData['values'] as $repo) {
            echo "     - " . $repo['name'] . " (" . $repo['full_name'] . ")\n";
        }
        
        // Test 3: Try to download a specific repository
        if (!empty($repoData['values'])) {
            $testRepo = $repoData['values'][0]['name'];
            echo "\n3. Testing download of repository: $testRepo\n";
            
            // Try different download URL formats
            $downloadUrls = array(
                "https://bitbucket.org/$workspace/$testRepo/get/master.zip",
                "https://api.bitbucket.org/2.0/repositories/$workspace/$testRepo/downloads",
                "https://bitbucket.org/$workspace/$testRepo/get/HEAD.zip"
            );
            
            foreach ($downloadUrls as $i => $downloadUrl) {
                echo "   Testing URL " . ($i + 1) . ": $downloadUrl\n";
                
                $ch = curl_init();
                curl_setopt_array($ch, array(
                    CURLOPT_URL => $downloadUrl,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_FOLLOWLOCATION => false, // Don't follow redirects to see what happens
                    CURLOPT_TIMEOUT => 30,
                    CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
                    CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
                    CURLOPT_USERPWD => $username . ':' . $password,
                    CURLOPT_SSL_VERIFYPEER => true,
                    CURLOPT_SSL_VERIFYHOST => 2,
                    CURLOPT_HEADER => true
                ));
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
                $error = curl_error($ch);
                curl_close($ch);
                
                echo "     HTTP Code: $httpCode\n";
                echo "     Content Type: $contentType\n";
                echo "     cURL Error: " . ($error ?: 'None') . "\n";
                
                if ($httpCode === 302) {
                    // Extract redirect location
                    if (preg_match('/Location: (.+)/i', $response, $matches)) {
                        echo "     Redirect to: " . trim($matches[1]) . "\n";
                    }
                }
                
                echo "\n";
            }
        }
        
    } else {
        echo "   ❌ Repository listing failed\n";
        echo "   Response: " . substr($response, 0, 200) . "\n";
    }
}

echo "=== API Test Complete ===\n";
?>
