<?php
/**
 * Test Repository Access and Download
 * Focus on repository operations without user account access
 */

echo "=== Repository Access Test ===\n\n";

// Configuration with proper credentials
$username = 'mkonstantinov';
$password = 'ATBB8apM9szgWyfhk2FCfLSXt2Dp8A61F116';
$workspace = 'pfg';
$project = 'LABS';

echo "Username: $username\n";
echo "Workspace: $workspace\n";
echo "Project: $project\n\n";

// Test 1: List repositories in workspace
echo "1. Testing repository listing...\n";

$ch = curl_init();
curl_setopt_array($ch, array(
    CURLOPT_URL => "https://api.bitbucket.org/2.0/repositories/$workspace?q=" . urlencode('project.key="' . $project . '"') . "&sort=name&pagelen=50",
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
    CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
    CURLOPT_USERPWD => $username . ':' . $password,
    CURLOPT_HTTPHEADER => array(
        'Accept: application/json',
        'Content-Type: application/json'
    ),
    CURLOPT_SSL_VERIFYPEER => true,
    CURLOPT_SSL_VERIFYHOST => 2
));

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "   HTTP Code: $httpCode\n";
echo "   cURL Error: " . ($error ?: 'None') . "\n";

if ($httpCode === 200) {
    $repoData = json_decode($response, true);
    echo "   ✅ Repository listing successful!\n";
    echo "   Found " . count($repoData['values']) . " repositories:\n";
    
    $repositories = array();
    foreach ($repoData['values'] as $repo) {
        echo "     - " . $repo['name'] . " (slug: " . $repo['full_name'] . ")\n";
        // Extract the slug from full_name (pfg/repository-slug)
        $slug = explode('/', $repo['full_name'])[1];
        $repositories[] = array(
            'name' => $repo['name'],
            'slug' => $slug,
            'full_name' => $repo['full_name']
        );
    }

    echo "\n   Available repositories for download:\n";
    foreach ($repositories as $repo) {
        echo "     * " . $repo['name'] . " -> " . $repo['slug'] . "\n";
    }
    
    // Test 2: Try to download the first few repositories using their slugs
    $testRepos = array_slice($repositories, 0, 3); // Test first 3 repositories

    foreach ($testRepos as $repo) {
        echo "\n2. Testing download of repository: " . $repo['name'] . " (slug: " . $repo['slug'] . ")\n";
        $success = testRepositoryDownload($repo['slug'], $username, $password, $workspace);
        if ($success) {
            echo "   ✅ Successfully downloaded and validated " . $repo['name'] . "\n";
        } else {
            echo "   ❌ Failed to download " . $repo['name'] . "\n";
        }
    }
    
} else {
    echo "   ❌ Repository listing failed\n";
    echo "   Response: " . substr($response, 0, 500) . "\n";
}

/**
 * Test repository download
 */
function testRepositoryDownload($repoName, $username, $password, $workspace) {
    $ref = 'master';
    
    // Try different download approaches
    $downloadMethods = array(
        array(
            'name' => 'Direct ZIP download',
            'url' => "https://bitbucket.org/$workspace/$repoName/get/$ref.zip"
        ),
        array(
            'name' => 'API archive download',
            'url' => "https://api.bitbucket.org/2.0/repositories/$workspace/$repoName/downloads"
        ),
        array(
            'name' => 'Alternative ZIP format',
            'url' => "https://bitbucket.org/$workspace/$repoName/get/HEAD.zip"
        )
    );
    
    foreach ($downloadMethods as $i => $method) {
        echo "   Method " . ($i + 1) . ": " . $method['name'] . "\n";
        echo "   URL: " . $method['url'] . "\n";
        
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $method['url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 5,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
            CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
            CURLOPT_USERPWD => $username . ':' . $password,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_SSL_VERIFYHOST => 2,
            CURLOPT_HEADER => false
        ));
        
        $data = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
        $downloadSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
        $error = curl_error($ch);
        curl_close($ch);
        
        echo "     HTTP Code: $httpCode\n";
        echo "     Content Type: $contentType\n";
        echo "     Size: " . number_format($downloadSize) . " bytes\n";
        echo "     cURL Error: " . ($error ?: 'None') . "\n";
        
        if ($httpCode === 200 && $downloadSize > 0) {
            echo "     ✅ Download successful!\n";
            
            // Check if it's a valid ZIP file
            $zipSignature = substr($data, 0, 4);
            $isZip = ($zipSignature === "PK\x03\x04" || $zipSignature === "PK\x05\x06" || $zipSignature === "PK\x07\x08");
            
            if ($isZip) {
                echo "     ✅ Valid ZIP file detected\n";
                
                // Save and test the ZIP
                $tempFile = "/tmp/test-{$repoName}-" . time() . ".zip";
                file_put_contents($tempFile, $data);
                
                $zip = new ZipArchive();
                $result = $zip->open($tempFile);
                
                if ($result === TRUE) {
                    echo "     ✅ ZIP file is valid and contains " . $zip->numFiles . " files\n";
                    
                    // Show first few files
                    echo "     Sample files:\n";
                    for ($j = 0; $j < min(5, $zip->numFiles); $j++) {
                        $stat = $zip->statIndex($j);
                        echo "       - " . $stat['name'] . "\n";
                    }
                    $zip->close();
                } else {
                    echo "     ❌ ZIP file is corrupted (error code: $result)\n";
                }
                
                unlink($tempFile);
                echo "     File cleaned up\n";
                
                // Success - we found a working method
                return true;
                
            } else {
                echo "     ❌ Downloaded data is not a ZIP file\n";
                echo "     First 100 chars: " . substr($data, 0, 100) . "\n";
            }
        } else {
            echo "     ❌ Download failed\n";
            if (!empty($data) && strlen($data) < 1000) {
                echo "     Response: " . substr($data, 0, 500) . "\n";
            }
        }
        
        echo "\n";
    }
    
    return false;
}

echo "\n=== Repository Access Test Complete ===\n";
?>
