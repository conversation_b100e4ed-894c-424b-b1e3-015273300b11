<?php
/**
 * Test Installation Fixes
 * Comprehensive test of the fixed PFG Core installation process
 */

echo "=== Testing PFG Core Installation Fixes ===\n\n";

try {
    require_once 'app/Mage.php';
    Mage::app();
    
    // Test repository: cloudflare-integration
    $repositoryName = 'cloudflare-integration';
    $version = 'master';
    
    echo "Testing complete installation process for: $repositoryName\n";
    echo str_repeat("=", 70) . "\n";
    
    // Step 1: Test module name detection from ZIP
    echo "1. Testing module name detection...\n";
    
    $repositoryModel = Mage::getModel('pfg_core/repository');
    $repoDetails = $repositoryModel->getRepositoryDetails($repositoryName);
    
    if (!$repoDetails['success']) {
        throw new Exception('Failed to get repository details: ' . $repoDetails['error']);
    }
    
    $repo = $repoDetails['data'];
    $expectedModuleName = $repo['module_name'];
    
    // Download the repository
    $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
    $repositoryIdentifier = $repo['repository_identifier'];
    $downloadResult = $bitbucketHelper->downloadRepository($repositoryIdentifier, $version);
    
    if (!$downloadResult['success']) {
        throw new Exception('Failed to download repository: ' . $downloadResult['error']);
    }
    
    // Test module name detection
    $installationModel = Mage::getModel('pfg_core/installation');
    $reflection = new ReflectionClass($installationModel);
    $method = $reflection->getMethod('_detectModuleNameFromZip');
    $method->setAccessible(true);
    
    $actualModuleName = $method->invoke($installationModel, $downloadResult['filepath']);
    
    echo "  Expected module name: $expectedModuleName\n";
    echo "  Detected module name: " . ($actualModuleName ?: 'NONE') . "\n";
    echo "  Detection result: " . ($actualModuleName ? '✅ SUCCESS' : '❌ FAILED') . "\n\n";
    
    // Step 2: Test file extraction and structure detection
    echo "2. Testing file extraction and structure detection...\n";
    
    $tempDir = Mage::helper('pfg_core')->getTempDir() . DS . 'test_install_' . time();
    if (!mkdir($tempDir, 0755, true)) {
        throw new Exception('Failed to create temp directory');
    }
    
    $zip = new ZipArchive();
    $zip->open($downloadResult['filepath']);
    $zip->extractTo($tempDir);
    $zip->close();
    
    $extractedDirs = glob($tempDir . DS . '*', GLOB_ONLYDIR);
    $sourceDir = $extractedDirs[0];
    
    echo "  Source directory: $sourceDir\n";
    
    // Test module file detection with actual module name
    if ($actualModuleName) {
        $moduleCodeDir = $sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $actualModuleName);
        $moduleFile = $sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $actualModuleName . '.xml';
        
        echo "  Expected module code dir: $moduleCodeDir\n";
        echo "  Exists: " . (is_dir($moduleCodeDir) ? '✅ Yes' : '❌ No') . "\n";
        
        echo "  Expected module file: $moduleFile\n";
        echo "  Exists: " . (file_exists($moduleFile) ? '✅ Yes' : '❌ No') . "\n";
        
        // Search for actual files
        $actualModuleDirs = glob($sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . 'PFG' . DS . '*', GLOB_ONLYDIR);
        $actualModuleFiles = glob($sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . 'PFG_*.xml');
        
        echo "  Actual module directories found:\n";
        foreach ($actualModuleDirs as $dir) {
            echo "    " . basename($dir) . "\n";
        }
        
        echo "  Actual module files found:\n";
        foreach ($actualModuleFiles as $file) {
            echo "    " . basename($file) . "\n";
        }
    }
    echo "\n";
    
    // Step 3: Test installation simulation (without actually installing)
    echo "3. Testing installation simulation...\n";
    
    $log = array();
    $baseDir = Mage::getBaseDir();
    
    // Simulate the improved installation logic
    $moduleCodeInstalled = false;
    $moduleFileInstalled = false;
    
    if ($actualModuleName) {
        // Test exact match first
        $moduleCodeDir = $sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $actualModuleName);
        if (is_dir($moduleCodeDir)) {
            $targetDir = $baseDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $actualModuleName);
            echo "  ✅ Would install module code: $moduleCodeDir → $targetDir\n";
            $moduleCodeInstalled = true;
        } else {
            // Search for PFG modules
            $pfgDirs = glob($sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . 'PFG' . DS . '*', GLOB_ONLYDIR);
            foreach ($pfgDirs as $pfgModuleDir) {
                $moduleSubName = basename($pfgModuleDir);
                $targetDir = $baseDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . 'PFG' . DS . $moduleSubName;
                echo "  ✅ Would install module code: $pfgModuleDir → $targetDir\n";
                $moduleCodeInstalled = true;
            }
        }
        
        // Test module declaration
        $moduleFile = $sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $actualModuleName . '.xml';
        if (file_exists($moduleFile)) {
            $targetFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $actualModuleName . '.xml';
            echo "  ✅ Would install module declaration: $moduleFile → $targetFile\n";
            $moduleFileInstalled = true;
        } else {
            // Search for PFG module files
            $moduleFiles = glob($sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . 'PFG_*.xml');
            foreach ($moduleFiles as $moduleFile) {
                $filename = basename($moduleFile);
                $targetFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $filename;
                echo "  ✅ Would install module declaration: $moduleFile → $targetFile\n";
                $moduleFileInstalled = true;
            }
        }
    }
    
    echo "  Module code installation: " . ($moduleCodeInstalled ? '✅ Ready' : '❌ Failed') . "\n";
    echo "  Module declaration installation: " . ($moduleFileInstalled ? '✅ Ready' : '❌ Failed') . "\n";
    echo "  Overall installation readiness: " . (($moduleCodeInstalled && $moduleFileInstalled) ? '✅ READY' : '❌ NOT READY') . "\n\n";
    
    // Step 4: Test backup creation
    echo "4. Testing backup creation...\n";
    
    if ($actualModuleName) {
        $backupModel = Mage::getModel('pfg_core/backup');
        $backupResult = $backupModel->createBackup($actualModuleName, 'test', 'Test backup for installation fixes');
        
        if ($backupResult['success']) {
            echo "  ✅ Backup creation successful\n";
            echo "  Backup ID: " . $backupResult['backup_id'] . "\n";
            echo "  Backup path: " . (isset($backupResult['backup_path']) ? $backupResult['backup_path'] : 'N/A') . "\n";
        } else {
            echo "  ❌ Backup creation failed: " . $backupResult['error'] . "\n";
        }
    } else {
        echo "  ⚠️  Skipped - no module name detected\n";
    }
    echo "\n";
    
    // Step 5: Test installation record creation
    echo "5. Testing installation record creation...\n";
    
    if ($actualModuleName) {
        $testInstallation = Mage::getModel('pfg_core/installation');
        $testInstallation->setData(array(
            'module_name' => $actualModuleName,
            'repository_name' => $repositoryName,
            'version_available' => $version,
            'installation_status' => 'test',
            'installation_type' => 'test',
            'installed_by' => 'test_script'
        ));
        
        try {
            $testInstallation->save();
            echo "  ✅ Installation record created successfully\n";
            echo "  Installation ID: " . $testInstallation->getId() . "\n";
            echo "  Module name in record: " . $testInstallation->getModuleName() . "\n";
            
            // Clean up test record
            $testInstallation->delete();
            echo "  🗑️  Test record cleaned up\n";
        } catch (Exception $e) {
            echo "  ❌ Installation record creation failed: " . $e->getMessage() . "\n";
        }
    } else {
        echo "  ⚠️  Skipped - no module name detected\n";
    }
    echo "\n";
    
    // Cleanup
    echo "6. Cleaning up...\n";
    function removeDirectory($dir) {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), array('.', '..'));
            foreach ($files as $file) {
                $path = $dir . DS . $file;
                is_dir($path) ? removeDirectory($path) : unlink($path);
            }
            rmdir($dir);
        }
    }
    
    removeDirectory($tempDir);
    unlink($downloadResult['filepath']);
    echo "  ✅ Temporary files cleaned up\n\n";
    
    // Summary
    echo str_repeat("=", 70) . "\n";
    echo "INSTALLATION FIXES SUMMARY\n";
    echo str_repeat("=", 70) . "\n";
    
    if ($actualModuleName && $moduleCodeInstalled && $moduleFileInstalled) {
        echo "🎉 ALL FIXES WORKING CORRECTLY!\n\n";
        echo "✅ Module name detection: Working\n";
        echo "✅ File structure detection: Working\n";
        echo "✅ Installation simulation: Ready\n";
        echo "✅ Backup creation: Working\n";
        echo "✅ Installation records: Working\n\n";
        echo "The PFG Core installation process should now work correctly!\n";
    } else {
        echo "❌ SOME ISSUES REMAIN\n\n";
        echo "Module name detection: " . ($actualModuleName ? '✅' : '❌') . "\n";
        echo "Module code installation: " . ($moduleCodeInstalled ? '✅' : '❌') . "\n";
        echo "Module file installation: " . ($moduleFileInstalled ? '✅' : '❌') . "\n";
        echo "\nPlease review the issues above.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
