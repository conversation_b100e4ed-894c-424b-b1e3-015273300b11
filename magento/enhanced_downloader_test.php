<?php
/**
 * Enhanced Downloader Tester
 * Comprehensive testing suite for Bitbucket repository downloads
 * 
 * Features:
 * - Multiple test modes (standalone, with Magento, full integration)
 * - Detailed logging and reporting
 * - Performance metrics
 * - Error analysis and troubleshooting
 * - Batch testing capabilities
 * - Configuration validation
 */

// Configuration
$testMode = isset($argv[1]) ? $argv[1] : 'auto'; // auto, standalone, magento, full
$repositoryName = isset($argv[2]) ? $argv[2] : null;
$verbose = in_array('--verbose', $argv) || in_array('-v', $argv);
$skipCleanup = in_array('--no-cleanup', $argv);

/**
 * Show usage information
 */
function showUsage() {
    echo "Enhanced Downloader Tester\n\n";
    echo "Usage: php enhanced_downloader_test.php [mode] [repository] [options]\n\n";
    echo "Modes:\n";
    echo "  auto       - Auto-detect Magento (default)\n";
    echo "  standalone - Run without <PERSON>gento\n";
    echo "  magento    - Require Magento environment\n";
    echo "  full       - Full test suite with performance tests\n\n";
    echo "Options:\n";
    echo "  --verbose, -v    - Verbose output\n";
    echo "  --no-cleanup     - Don't delete downloaded files\n";
    echo "  --help, -h       - Show this help\n\n";
    echo "Examples:\n";
    echo "  php enhanced_downloader_test.php\n";
    echo "  php enhanced_downloader_test.php full\n";
    echo "  php enhanced_downloader_test.php standalone pfg-cloudflare-integration\n";
    echo "  php enhanced_downloader_test.php magento --verbose\n";
}

// Handle help first
if (in_array('--help', $argv) || in_array('-h', $argv)) {
    showUsage();
    exit(0);
}

echo "=== Enhanced Downloader Tester ===\n\n";

// Test mode detection and setup
$useMagento = false;
$magentoAvailable = false;

if ($testMode === 'auto' || $testMode === 'magento' || $testMode === 'full') {
    if (file_exists('app/Mage.php')) {
        try {
            require_once 'app/Mage.php';
            Mage::app();
            $magentoAvailable = true;
            $useMagento = true;
            echo "✅ Magento environment loaded\n";
        } catch (Exception $e) {
            echo "⚠️  Magento environment failed to load: " . $e->getMessage() . "\n";
            if ($testMode === 'magento' || $testMode === 'full') {
                echo "❌ Cannot continue in Magento mode\n";
                exit(1);
            }
        }
    } else {
        echo "⚠️  Magento not found (app/Mage.php missing)\n";
        if ($testMode === 'magento' || $testMode === 'full') {
            echo "❌ Cannot continue in Magento mode\n";
            exit(1);
        }
    }
}

if ($testMode === 'standalone' || !$magentoAvailable) {
    $useMagento = false;
    echo "🔧 Running in standalone mode\n";
}

echo "Mode: " . ($useMagento ? 'Magento Integration' : 'Standalone') . "\n\n";

// Test results storage
$testResults = array(
    'start_time' => microtime(true),
    'tests' => array(),
    'summary' => array(
        'total' => 0,
        'passed' => 0,
        'failed' => 0,
        'warnings' => 0
    )
);

/**
 * Add test result
 */
function addTestResult($name, $status, $message, $details = array(), $duration = 0) {
    global $testResults, $verbose;
    
    $testResults['tests'][] = array(
        'name' => $name,
        'status' => $status, // 'pass', 'fail', 'warning'
        'message' => $message,
        'details' => $details,
        'duration' => $duration,
        'timestamp' => microtime(true)
    );
    
    $testResults['summary']['total']++;
    if ($status === 'pass') {
        $testResults['summary']['passed']++;
        $icon = '✅';
    } elseif ($status === 'fail') {
        $testResults['summary']['failed']++;
        $icon = '❌';
    } else {
        $testResults['summary']['warnings']++;
        $icon = '⚠️ ';
    }
    
    echo sprintf("%s %s: %s", $icon, $name, $message);
    if ($duration > 0) {
        echo sprintf(" (%.2fs)", $duration);
    }
    echo "\n";
    
    if ($verbose && !empty($details)) {
        foreach ($details as $key => $value) {
            echo "   $key: $value\n";
        }
    }
}

/**
 * Decrypt Magento encrypted password
 */
function decryptMagentoPassword($encryptedData, $key) {
    try {
        // Magento 1.9 uses base64 encoding with simple encryption
        $data = base64_decode($encryptedData);

        if ($data === false) {
            return '';
        }

        // Try simple XOR decryption (common in Magento 1.x)
        $keyLength = strlen($key);
        $dataLength = strlen($data);
        $decrypted = '';

        for ($i = 0; $i < $dataLength; $i++) {
            $decrypted .= $data[$i] ^ $key[$i % $keyLength];
        }

        // Clean up any null bytes or control characters
        $decrypted = trim($decrypted, "\0..\37");

        return $decrypted;
    } catch (Exception $e) {
        return '';
    }
}

/**
 * Get configuration based on mode
 */
function getConfiguration($useMagento) {
    if ($useMagento) {
        try {
            $helper = Mage::helper('pfg_core');
            return array(
                'username' => $helper->getBitbucketUsername(),
                'password' => $helper->getBitbucketAppPassword(),
                'workspace' => $helper->getBitbucketWorkspace(),
                'project' => $helper->getBitbucketProject(),
                'timeout' => $helper->getApiTimeout(),
                'temp_dir' => $helper->getTempDir(),
                'has_credentials' => $helper->hasCredentials()
            );
        } catch (Exception $e) {
            addTestResult('Configuration Load', 'fail', 'Failed to load Magento configuration: ' . $e->getMessage());
            return false;
        }
    } else {
        // Standalone configuration - with proper credentials
        return array(
            'username' => 'mkonstantinov',
            'password' => 'ATBB8apM9szgWyfhk2FCfLSXt2Dp8A61F116',
            'workspace' => 'pfg',
            'project' => 'LABS',
            'timeout' => 30,
            'temp_dir' => '/tmp',
            'has_credentials' => true
        );
    }
}

// Load configuration
$config = getConfiguration($useMagento);
if ($config === false) {
    exit(1);
}

// Test 1: Configuration Validation
$startTime = microtime(true);
$configDetails = array();
foreach ($config as $key => $value) {
    if ($key === 'password') {
        $configDetails[$key] = empty($value) ? 'NOT SET' : 'SET (' . strlen($value) . ' chars)';
    } else {
        $configDetails[$key] = $value;
    }
}

if (empty($config['username']) || empty($config['password'])) {
    addTestResult('Configuration', 'fail', 'Missing credentials', $configDetails, microtime(true) - $startTime);
    if (!$useMagento) {
        echo "\n💡 For standalone mode, please edit this script and set the credentials in the getConfiguration() function\n";
    }
    exit(1);
} else {
    addTestResult('Configuration', 'pass', 'Credentials configured', $configDetails, microtime(true) - $startTime);
}

// Test 2: API Connection Test
if ($useMagento && $testMode === 'full') {
    $startTime = microtime(true);
    try {
        $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
        $connectionTest = $bitbucketHelper->testConnection();
        
        if ($connectionTest['success']) {
            addTestResult('API Connection', 'pass', $connectionTest['message'], array(), microtime(true) - $startTime);
        } else {
            addTestResult('API Connection', 'fail', $connectionTest['message'], array(), microtime(true) - $startTime);
        }
    } catch (Exception $e) {
        addTestResult('API Connection', 'fail', 'Exception: ' . $e->getMessage(), array(), microtime(true) - $startTime);
    }
}

// Test 3: Repository List Test
if ($useMagento && $testMode === 'full') {
    $startTime = microtime(true);
    try {
        $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
        $repositories = $bitbucketHelper->getRepositories();
        
        if ($repositories['success']) {
            $repoCount = count($repositories['data']);
            $repoNames = array_map(function($repo) { return $repo['name']; }, $repositories['data']);
            addTestResult('Repository List', 'pass', "Found $repoCount repositories", array('repositories' => implode(', ', $repoNames)), microtime(true) - $startTime);
            
            // Store for later use
            $availableRepos = $repositories['data'];
        } else {
            addTestResult('Repository List', 'fail', $repositories['error'], array(), microtime(true) - $startTime);
            $availableRepos = array();
        }
    } catch (Exception $e) {
        addTestResult('Repository List', 'fail', 'Exception: ' . $e->getMessage(), array(), microtime(true) - $startTime);
        $availableRepos = array();
    }
}

// Test 4: Download Test
$testRepos = array();
if ($repositoryName) {
    $testRepos[] = $repositoryName;
} else {
    // Default test repositories (using correct slugs and branches)
    $defaultRepos = array('cloudflare-integration', 'pfg-log-reader', 'pfg-analytics');
    if ($useMagento && isset($availableRepos)) {
        // Use first available repo from API
        $testRepos[] = !empty($availableRepos) ? $availableRepos[0]['name'] : $defaultRepos[0];
    } else {
        $testRepos = $defaultRepos;
    }
}

foreach ($testRepos as $repoName) {
    testRepositoryDownload($repoName, $config, $useMagento);
}

/**
 * Test repository download
 */
function testRepositoryDownload($repositoryName, $config, $useMagento) {
    global $skipCleanup, $verbose;

    echo "\n--- Testing Repository: $repositoryName ---\n";

    $startTime = microtime(true);
    $ref = 'master';

    if ($useMagento) {
        // Use Magento helper
        try {
            $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
            $downloadResult = $bitbucketHelper->downloadRepository($repositoryName, $ref);

            if ($downloadResult['success']) {
                $details = array(
                    'file' => $downloadResult['filepath'],
                    'size' => number_format($downloadResult['size']) . ' bytes'
                );
                addTestResult("Download ($repositoryName)", 'pass', 'Download successful', $details, microtime(true) - $startTime);

                // Test ZIP file
                testZipFile($downloadResult['filepath'], $repositoryName);

                // Cleanup
                if (!$skipCleanup && file_exists($downloadResult['filepath'])) {
                    unlink($downloadResult['filepath']);
                    if ($verbose) echo "   Cleaned up: " . $downloadResult['filepath'] . "\n";
                }
            } else {
                addTestResult("Download ($repositoryName)", 'fail', $downloadResult['error'], array(), microtime(true) - $startTime);
            }
        } catch (Exception $e) {
            addTestResult("Download ($repositoryName)", 'fail', 'Exception: ' . $e->getMessage(), array(), microtime(true) - $startTime);
        }
    } else {
        // Direct cURL download
        testDirectDownload($repositoryName, $ref, $config);
    }
}

/**
 * Test direct cURL download (standalone mode)
 */
function testDirectDownload($repositoryName, $ref, $config) {
    global $skipCleanup, $verbose;

    $startTime = microtime(true);
    $downloadUrl = sprintf('https://bitbucket.org/%s/%s/get/%s.zip', $config['workspace'], $repositoryName, $ref);

    if ($verbose) {
        echo "   Download URL: $downloadUrl\n";
    }

    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => $downloadUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => $config['timeout'],
        CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
        CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
        CURLOPT_USERPWD => $config['username'] . ':' . $config['password'],
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2
    ));

    $zipData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $downloadSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
    $error = curl_error($ch);
    curl_close($ch);

    $details = array(
        'http_code' => $httpCode,
        'content_type' => $contentType,
        'size' => number_format($downloadSize) . ' bytes',
        'curl_error' => $error ?: 'None'
    );

    if ($error) {
        addTestResult("Download ($repositoryName)", 'fail', 'cURL error: ' . $error, $details, microtime(true) - $startTime);
        return;
    }

    if ($httpCode !== 200) {
        $errorMsg = "HTTP error: $httpCode";
        if ($httpCode === 401) {
            $errorMsg .= ' (Authentication failed)';
        } elseif ($httpCode === 404) {
            $errorMsg .= ' (Repository not found)';
        }
        addTestResult("Download ($repositoryName)", 'fail', $errorMsg, $details, microtime(true) - $startTime);
        return;
    }

    if (empty($zipData)) {
        addTestResult("Download ($repositoryName)", 'fail', 'No data received', $details, microtime(true) - $startTime);
        return;
    }

    // Save to temp file
    $filename = sprintf('%s-%s-%s.zip', $repositoryName, $ref, time());
    $filepath = $config['temp_dir'] . '/' . $filename;

    if (file_put_contents($filepath, $zipData) === false) {
        addTestResult("Download ($repositoryName)", 'fail', 'Failed to save file', $details, microtime(true) - $startTime);
        return;
    }

    $details['file'] = $filepath;
    addTestResult("Download ($repositoryName)", 'pass', 'Download successful', $details, microtime(true) - $startTime);

    // Test ZIP file
    testZipFile($filepath, $repositoryName);

    // Cleanup
    if (!$skipCleanup && file_exists($filepath)) {
        unlink($filepath);
        if ($verbose) echo "   Cleaned up: $filepath\n";
    }
}

/**
 * Test ZIP file validity
 */
function testZipFile($filepath, $repositoryName) {
    global $verbose;

    $startTime = microtime(true);

    if (!file_exists($filepath)) {
        addTestResult("ZIP Test ($repositoryName)", 'fail', 'File does not exist', array(), microtime(true) - $startTime);
        return;
    }

    $fileSize = filesize($filepath);
    if ($fileSize === false || $fileSize < 100) {
        addTestResult("ZIP Test ($repositoryName)", 'fail', 'File too small or invalid', array('size' => $fileSize . ' bytes'), microtime(true) - $startTime);
        return;
    }

    // Check ZIP signature
    $handle = fopen($filepath, 'rb');
    $signature = fread($handle, 4);
    fclose($handle);

    $isValidSignature = ($signature === "PK\x03\x04" || $signature === "PK\x05\x06" || $signature === "PK\x07\x08");

    if (!$isValidSignature) {
        addTestResult("ZIP Test ($repositoryName)", 'fail', 'Invalid ZIP signature', array('signature' => bin2hex($signature)), microtime(true) - $startTime);
        return;
    }

    // Test with ZipArchive
    $zip = new ZipArchive();
    $result = $zip->open($filepath, ZipArchive::CHECKCONS);

    if ($result !== TRUE) {
        $zipErrors = array(
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_OPEN => 'Cannot open file',
            ZipArchive::ER_READ => 'Read error'
        );

        $errorMsg = isset($zipErrors[$result]) ? $zipErrors[$result] : 'Unknown error';
        addTestResult("ZIP Test ($repositoryName)", 'fail', $errorMsg, array('error_code' => $result), microtime(true) - $startTime);
        return;
    }

    $numFiles = $zip->numFiles;
    $sampleFiles = array();

    for ($i = 0; $i < min(5, $numFiles); $i++) {
        $stat = $zip->statIndex($i);
        $sampleFiles[] = $stat['name'];
    }

    $zip->close();

    $details = array(
        'file_count' => $numFiles,
        'sample_files' => implode(', ', $sampleFiles)
    );

    addTestResult("ZIP Test ($repositoryName)", 'pass', "Valid ZIP with $numFiles files", $details, microtime(true) - $startTime);
}

// Performance Test (if in full mode)
if ($testMode === 'full' && !empty($testRepos)) {
    echo "\n--- Performance Test ---\n";
    $performanceTest = array();

    foreach ($testRepos as $repoName) {
        $times = array();
        $iterations = 3;

        echo "Testing $repoName ($iterations iterations)...\n";

        for ($i = 0; $i < $iterations; $i++) {
            $startTime = microtime(true);

            if ($useMagento) {
                $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
                $result = $bitbucketHelper->downloadRepository($repoName, 'master');
                if ($result['success'] && file_exists($result['filepath'])) {
                    unlink($result['filepath']); // Cleanup immediately
                }
            } else {
                testDirectDownload($repoName, 'master', $config);
            }

            $times[] = microtime(true) - $startTime;
        }

        $avgTime = array_sum($times) / count($times);
        $minTime = min($times);
        $maxTime = max($times);

        $performanceTest[$repoName] = array(
            'average' => $avgTime,
            'min' => $minTime,
            'max' => $maxTime,
            'iterations' => $iterations
        );

        echo sprintf("   Average: %.2fs, Min: %.2fs, Max: %.2fs\n", $avgTime, $minTime, $maxTime);
    }
}

// Final Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "TEST SUMMARY\n";
echo str_repeat("=", 50) . "\n";

$totalTime = microtime(true) - $testResults['start_time'];
$summary = $testResults['summary'];

echo sprintf("Total Tests: %d\n", $summary['total']);
echo sprintf("Passed: %d (%.1f%%)\n", $summary['passed'], $summary['total'] > 0 ? ($summary['passed'] / $summary['total']) * 100 : 0);
echo sprintf("Failed: %d (%.1f%%)\n", $summary['failed'], $summary['total'] > 0 ? ($summary['failed'] / $summary['total']) * 100 : 0);
echo sprintf("Warnings: %d (%.1f%%)\n", $summary['warnings'], $summary['total'] > 0 ? ($summary['warnings'] / $summary['total']) * 100 : 0);
echo sprintf("Total Time: %.2f seconds\n", $totalTime);

if ($summary['failed'] > 0) {
    echo "\nFAILED TESTS:\n";
    foreach ($testResults['tests'] as $test) {
        if ($test['status'] === 'fail') {
            echo sprintf("❌ %s: %s\n", $test['name'], $test['message']);
        }
    }
}

if ($summary['warnings'] > 0) {
    echo "\nWARNINGS:\n";
    foreach ($testResults['tests'] as $test) {
        if ($test['status'] === 'warning') {
            echo sprintf("⚠️  %s: %s\n", $test['name'], $test['message']);
        }
    }
}

// Performance summary
if (isset($performanceTest) && !empty($performanceTest)) {
    echo "\nPERFORMANCE SUMMARY:\n";
    foreach ($performanceTest as $repo => $stats) {
        echo sprintf("%s: %.2fs avg (%.2fs - %.2fs)\n", $repo, $stats['average'], $stats['min'], $stats['max']);
    }
}

echo "\n";

// Exit with appropriate code
if ($summary['failed'] > 0) {
    echo "❌ Some tests failed!\n";
    exit(1);
} elseif ($summary['warnings'] > 0) {
    echo "⚠️  Tests completed with warnings\n";
    exit(2);
} else {
    echo "✅ All tests passed!\n";
    exit(0);
}

?>
