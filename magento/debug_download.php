<?php
/**
 * Debug Download Script
 * Test the actual download process step by step
 */

// Include Magento
require_once 'app/Mage.php';
Mage::app();

echo "=== Debug Download Process ===\n\n";

try {
    $helper = Mage::helper('pfg_core');
    $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
    
    // Test repository name
    $repositoryName = 'pfg-cloudflare-integration'; // Change this to test different repos
    $ref = 'master';

    echo "Testing download of: $repositoryName ($ref)\n\n";

    // Use direct credentials from database (bypass Magento helper issues)
    $workspace = 'pfg';
    $username = 'mkonstantinov';
    $encryptedPassword = 'nwJvdt7AHNVn0pO2W84ScSwxwCdmWn9cvsh2uEhaYCSwpdkkBlSJCw==';
    $cryptKey = '5d5d8440461ea06d354c51908037f2b3';

    // Simple decryption attempt
    $password = base64_decode($encryptedPassword);

    // Step 1: Build download URL
    $downloadUrl = sprintf('https://bitbucket.org/%s/%s/get/%s.zip', $workspace, $repositoryName, $ref);
    echo "1. Download URL: $downloadUrl\n";

    // Step 2: Test credentials
    echo "2. Username: $username\n";
    echo "3. Password: " . (empty($password) ? 'NOT SET' : 'SET (' . strlen($password) . ' chars)') . "\n\n";
    
    // Step 3: Test direct cURL download
    echo "4. Testing cURL download...\n";
    
    $ch = curl_init();
    curl_setopt_array($ch, array(
        CURLOPT_URL => $downloadUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_USERAGENT => 'PFG-Core-Module/1.0',
        CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
        CURLOPT_USERPWD => $username . ':' . $password,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_SSL_VERIFYHOST => 2,
        CURLOPT_VERBOSE => false
    ));
    
    $zipData = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $downloadSize = curl_getinfo($ch, CURLINFO_SIZE_DOWNLOAD);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "   HTTP Code: $httpCode\n";
    echo "   Content Type: $contentType\n";
    echo "   Download Size: " . number_format($downloadSize) . " bytes\n";
    echo "   cURL Error: " . ($error ?: 'None') . "\n\n";
    
    if ($httpCode !== 200) {
        echo "❌ HTTP Error: $httpCode\n";
        if ($httpCode === 401) {
            echo "   This is an authentication error. Check your credentials.\n";
        } elseif ($httpCode === 404) {
            echo "   Repository not found. Check repository name and workspace.\n";
        }
        exit(1);
    }
    
    if (empty($zipData)) {
        echo "❌ No data received\n";
        exit(1);
    }
    
    echo "✅ Download successful!\n\n";
    
    // Step 4: Analyze the downloaded data
    echo "5. Analyzing downloaded data...\n";
    
    // Check if it starts with ZIP signature
    $zipSignature = substr($zipData, 0, 4);
    $isZip = ($zipSignature === "PK\x03\x04" || $zipSignature === "PK\x05\x06" || $zipSignature === "PK\x07\x08");
    echo "   ZIP signature check: " . ($isZip ? '✅ Valid' : '❌ Invalid') . "\n";
    
    if (!$isZip) {
        echo "   First 100 chars of response:\n";
        echo "   " . substr($zipData, 0, 100) . "\n\n";
        
        // Check if it's HTML (error page)
        if (strpos($zipData, '<html') !== false || strpos($zipData, '<!DOCTYPE') !== false) {
            echo "   ❌ Response appears to be HTML (error page)\n";
        }
        exit(1);
    }
    
    // Step 5: Save and test ZIP file
    $tempDir = $helper->getTempDir();
    if (!is_dir($tempDir)) {
        mkdir($tempDir, 0755, true);
    }
    
    $testFile = $tempDir . '/test-download.zip';
    file_put_contents($testFile, $zipData);
    
    echo "6. Testing ZIP file: $testFile\n";
    
    $zip = new ZipArchive();
    $result = $zip->open($testFile);
    
    if ($result === TRUE) {
        echo "   ✅ ZIP file is valid\n";
        echo "   Files in archive: " . $zip->numFiles . "\n";
        
        // List first few files
        echo "   Sample files:\n";
        for ($i = 0; $i < min(10, $zip->numFiles); $i++) {
            $stat = $zip->statIndex($i);
            echo "     - " . $stat['name'] . "\n";
        }
        $zip->close();
        
        echo "\n✅ Download test successful!\n";
        echo "The ZIP file is valid and can be extracted.\n";
        
    } else {
        echo "   ❌ ZIP file is invalid (error code: $result)\n";
        
        $zipErrors = array(
            ZipArchive::ER_NOZIP => 'Not a zip archive',
            ZipArchive::ER_INCONS => 'Zip archive inconsistent',
            ZipArchive::ER_CRC => 'CRC error',
            ZipArchive::ER_OPEN => 'Can\'t open file',
            ZipArchive::ER_READ => 'Read error'
        );
        
        $errorMsg = isset($zipErrors[$result]) ? $zipErrors[$result] : 'Unknown error';
        echo "   Error: $errorMsg\n";
    }
    
    // Clean up
    if (file_exists($testFile)) {
        unlink($testFile);
        echo "   Test file cleaned up\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
?>
