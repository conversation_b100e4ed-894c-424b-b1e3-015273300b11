<?php
/**
 * Debug Installation Process
 * Diagnose why module installation reports success but doesn't actually install files
 */

echo "=== Debugging PFG Core Installation Process ===\n\n";

try {
    require_once 'app/Mage.php';
    Mage::app();
    
    // Test repository: cloudflare-integration
    $repositoryName = 'cloudflare-integration';
    $version = 'master';
    
    echo "Testing installation process for: $repositoryName\n";
    echo str_repeat("-", 60) . "\n";
    
    // Step 1: Get repository details
    echo "1. Getting repository details...\n";
    $repositoryModel = Mage::getModel('pfg_core/repository');
    $repoDetails = $repositoryModel->getRepositoryDetails($repositoryName);
    
    if (!$repoDetails['success']) {
        throw new Exception('Failed to get repository details: ' . $repoDetails['error']);
    }
    
    $repo = $repoDetails['data'];
    $moduleName = $repo['module_name'];
    
    echo "  ✅ Repository found\n";
    echo "  Name: " . $repo['name'] . "\n";
    echo "  Slug: " . $repo['slug'] . "\n";
    echo "  Module Name: $moduleName\n";
    echo "  Repository Identifier: " . $repo['repository_identifier'] . "\n\n";
    
    // Step 2: Download repository
    echo "2. Downloading repository...\n";
    $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
    $repositoryIdentifier = $repo['repository_identifier'];
    $downloadResult = $bitbucketHelper->downloadRepository($repositoryIdentifier, $version);
    
    if (!$downloadResult['success']) {
        throw new Exception('Failed to download repository: ' . $downloadResult['error']);
    }
    
    echo "  ✅ Download successful\n";
    echo "  File: " . $downloadResult['filepath'] . "\n";
    echo "  Size: " . number_format($downloadResult['size']) . " bytes\n\n";
    
    // Step 3: Examine the downloaded ZIP structure
    echo "3. Examining ZIP file structure...\n";
    $zip = new ZipArchive();
    $result = $zip->open($downloadResult['filepath']);
    
    if ($result !== TRUE) {
        throw new Exception('Cannot open ZIP file: ' . $result);
    }
    
    echo "  ✅ ZIP file opened successfully\n";
    echo "  Number of files: " . $zip->numFiles . "\n";
    echo "  Sample files:\n";
    
    $moduleFiles = array();
    $appFiles = array();
    
    for ($i = 0; $i < min(20, $zip->numFiles); $i++) {
        $stat = $zip->statIndex($i);
        $filename = $stat['name'];
        echo "    " . $filename . "\n";
        
        if (strpos($filename, 'app/code/') !== false) {
            $moduleFiles[] = $filename;
        }
        if (strpos($filename, 'app/') !== false) {
            $appFiles[] = $filename;
        }
    }
    
    $zip->close();
    echo "\n";
    
    // Step 4: Test extraction process
    echo "4. Testing extraction process...\n";
    $tempDir = Mage::helper('pfg_core')->getTempDir() . DS . 'debug_install_' . time();
    
    if (!mkdir($tempDir, 0755, true)) {
        throw new Exception('Failed to create temp directory: ' . $tempDir);
    }
    
    echo "  ✅ Temp directory created: $tempDir\n";
    
    $zip = new ZipArchive();
    $zip->open($downloadResult['filepath']);
    $zip->extractTo($tempDir);
    $zip->close();
    
    echo "  ✅ Files extracted\n";
    
    // Find extracted directory
    $extractedDirs = glob($tempDir . DS . '*', GLOB_ONLYDIR);
    if (empty($extractedDirs)) {
        throw new Exception('No directories found in extracted archive');
    }
    
    $sourceDir = $extractedDirs[0];
    echo "  Source directory: $sourceDir\n";
    
    // List contents of source directory
    echo "  Source directory contents:\n";
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($sourceDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    $fileCount = 0;
    foreach ($iterator as $file) {
        if ($fileCount < 15) { // Show first 15 files
            $relativePath = str_replace($sourceDir . DS, '', $file->getPathname());
            echo "    " . $relativePath . ($file->isDir() ? '/' : '') . "\n";
        }
        $fileCount++;
    }
    
    if ($fileCount > 15) {
        echo "    ... and " . ($fileCount - 15) . " more files\n";
    }
    echo "\n";
    
    // Step 5: Test module file detection
    echo "5. Testing module file detection...\n";
    
    // Check for expected module structure
    $expectedModuleCodeDir = $sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $moduleName);
    $expectedModuleFile = $sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
    
    echo "  Expected module code dir: $expectedModuleCodeDir\n";
    echo "  Exists: " . (is_dir($expectedModuleCodeDir) ? '✅ Yes' : '❌ No') . "\n";
    
    echo "  Expected module file: $expectedModuleFile\n";
    echo "  Exists: " . (file_exists($expectedModuleFile) ? '✅ Yes' : '❌ No') . "\n";
    
    // Look for actual module structure
    echo "  Searching for actual module structure...\n";
    $actualModuleDirs = glob($sourceDir . DS . 'app' . DS . 'code' . DS . '*' . DS . '*', GLOB_ONLYDIR);
    foreach ($actualModuleDirs as $dir) {
        echo "    Found: $dir\n";
    }
    
    $actualModuleFiles = glob($sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . '*.xml');
    foreach ($actualModuleFiles as $file) {
        echo "    Found: $file\n";
    }
    echo "\n";
    
    // Step 6: Test file permissions
    echo "6. Testing file permissions...\n";
    $baseDir = Mage::getBaseDir();
    $targetDirs = array(
        $baseDir . DS . 'app' . DS . 'code' . DS . 'local',
        $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules'
    );
    
    foreach ($targetDirs as $dir) {
        $writable = is_writable($dir);
        echo "  $dir: " . ($writable ? '✅ Writable' : '❌ Not writable') . "\n";
        
        if (!$writable) {
            echo "    Permissions: " . substr(sprintf('%o', fileperms($dir)), -4) . "\n";
            echo "    Owner: " . fileowner($dir) . "\n";
        }
    }
    echo "\n";
    
    // Step 7: Test actual installation simulation
    echo "7. Simulating installation...\n";
    $log = array();
    $baseDir = Mage::getBaseDir();
    $filesInstalled = 0;
    
    // Test module code installation
    $moduleCodeDir = $sourceDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $moduleName);
    if (is_dir($moduleCodeDir)) {
        $targetDir = $baseDir . DS . 'app' . DS . 'code' . DS . 'local' . DS . str_replace('_', DS, $moduleName);
        echo "  Would copy: $moduleCodeDir → $targetDir\n";
        $log[] = 'Would install module code to: ' . $targetDir;
    } else {
        echo "  ❌ Module code directory not found: $moduleCodeDir\n";
    }
    
    // Test module declaration installation
    $moduleFile = $sourceDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
    if (file_exists($moduleFile)) {
        $targetFile = $baseDir . DS . 'app' . DS . 'etc' . DS . 'modules' . DS . $moduleName . '.xml';
        echo "  Would copy: $moduleFile → $targetFile\n";
        $log[] = 'Would install module declaration: ' . $targetFile;
    } else {
        echo "  ❌ Module declaration file not found: $moduleFile\n";
    }
    
    // Cleanup
    echo "\n8. Cleaning up...\n";
    function removeDirectory($dir) {
        if (is_dir($dir)) {
            $files = array_diff(scandir($dir), array('.', '..'));
            foreach ($files as $file) {
                $path = $dir . DS . $file;
                is_dir($path) ? removeDirectory($path) : unlink($path);
            }
            rmdir($dir);
        }
    }
    
    removeDirectory($tempDir);
    unlink($downloadResult['filepath']);
    echo "  ✅ Temporary files cleaned up\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
?>
